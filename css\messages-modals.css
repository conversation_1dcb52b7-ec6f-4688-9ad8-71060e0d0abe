/**
 * Messages and Modals Styles
 * Toast messages, alerts, and modal dialogs
 */

/* Toast Messages */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    animation: slideInRight 0.3s ease-out;
}

/* ===== Pure CSS Modal (Bootstrap-free) ===== */
#presetModal.kms-modal {
    position: fixed;
    inset: 0;
    display: none;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.45);
    z-index: 1050;
}

#presetModal.kms-modal.is-open {
    display: flex;
}

#presetModal .kms-modal-dialog {
    width: min(1200px, 95vw);
    max-height: 90vh;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.35);
    background: #fff;
    display: flex;
    flex-direction: column;
}

#presetModal .kms-modal-header {
    background: #00a5ff;
    color: #ffffff;
    padding: 6px 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#presetModal .kms-modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

#presetModal .kms-modal-close {
    appearance: none;
    border: 0;
    background: transparent;
    color: #fff;
    font-size: 1.25rem;
    line-height: 1;
    padding: 0.25rem;
    border-radius: 6px;
    cursor: pointer;
}

#presetModal .kms-modal-close:hover {
    background: rgba(255, 255, 255, 0.15);
}

#presetModal .kms-modal-body {
    padding: 1rem;
    overflow: auto;
    background-color: #00a5ff;
}

#presetModal .kms-modal-footer {
    background: #00a5ff;
    padding: 6px 8px;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* ===== Preset Modal: form controls (pure CSS) ===== */
#presetModal .form-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 0.75rem 1rem;
}

#presetModal .col-6 {
    grid-column: span 6;
}

#presetModal .col-4 {
    grid-column: span 4;
}

#presetModal .col-12 {
    grid-column: span 12;
}

@media (max-width: 768px) {

    #presetModal .col-6,
    #presetModal .col-4 {
        grid-column: span 12;
    }
}

#presetModal .kms-label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.35rem;
}

#presetModal .kms-input,
#presetModal .kms-select,
#presetModal .kms-textarea {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ffffff;
    border-radius: 10px;
    background: #00a16d;
    color: #ffffff;
    margin-bottom: 3px;
}

#presetModal .kms-input:focus,
#presetModal .kms-select:focus,
#presetModal .kms-textarea:focus {
    outline: none;
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
}

#presetModal .input-inline {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
}

#presetModal .input-suffix {
    padding: 0.5rem 0.6rem;
    background: #f1f3f5;
    border: 1px solid #ced4da;
    border-radius: 8px;
}

#presetModal .toolbar-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

#presetModal .toolbar-row .grow {
    flex: 1 1 auto;
}

#presetModal .toolbar-row .kms-input,
#presetModal .toolbar-row .kms-select {
    max-width: 100%;
}

/* Body helper to prevent background scrolling when modal is open */
body.kms-no-scroll {
    overflow: hidden;
}

/* Preset list sizing within modal body */
#presetModal #presetList {
    min-height: 400px;
    max-height: 60vh;
    overflow-y: auto;
}

.toast-message.removing {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }

    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Alert Variants */
.alert-success {
    background-color: #d1e7dd;
    border-color: #badbcc;
    color: #0f5132;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #055160;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffecb5;
    color: #664d03;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c2c7;
    color: #842029;
}

/* Modal Enhancements */
.modal-header {
    background-color: #00a5ff;
    color: white;
    border-bottom: none;
    border-radius: 0.375rem 0.375rem 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-body {
    padding: 12px;
    background-color: #1fb5db;
}

.modal-footer {
    background-color: #1fb5db;
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 12px 12px;
}

/* Logo Preview Modal */
.logo-preview {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
}

.logo-preview .card {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo-preview .card-header {
    background: white;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.logo-preview img {
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

/* Default hidden until a logo is uploaded */
#logoPreview {
    display: none;
}

/* Constrain preview image size */
#logoPreviewImage {
    max-height: 200px;
    object-fit: contain;
}

/* Configuration Modal */
.config-section {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
}

.config-section h6 {
    color: #0d6efd;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #0d6efd;
}

.config-section:last-child {
    margin-bottom: 0;
}

/* Preset Items Modal */
.preset-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
    padding: 0.5rem;
}

.preset-item-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.preset-item-card:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
    transform: translateY(-2px);
}

.preset-item-card.selected {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.preset-item-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.preset-item-category {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.preset-item-price {
    font-weight: bold;
    color: #198754;
    font-family: 'Courier New', monospace;
}

/* Add Item Modal */
.add-item-form .form-group {
    margin-bottom: 1rem;
}

.add-item-form .form-label {
    font-weight: 600;
    color: #495057;
}

.add-item-form .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.price-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.price-preview {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.75rem;
    margin-top: 1rem;
}

.price-preview-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.price-preview-amount {
    font-size: 1.25rem;
    font-weight: bold;
    color: #198754;
    font-family: 'Courier New', monospace;
}

/* Loading States */
.modal-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.modal-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Error States */
.modal-error {
    text-align: center;
    padding: 2rem;
    color: #dc3545;
}

.modal-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Responsive Modal Adjustments */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem;
    }

    .preset-items-grid {
        grid-template-columns: 1fr;
    }

    .price-inputs {
        grid-template-columns: 1fr;
    }

    .toast-message {
        left: 10px;
        right: 10px;
        min-width: auto;
    }
}

/* Confirmation Dialogs */
.confirmation-dialog .modal-body {
    text-align: center;
    padding: 2rem;
}

.confirmation-dialog .modal-body i {
    font-size: 3rem;
    color: #ffc107;
    margin-bottom: 1rem;
}

.confirmation-dialog .modal-body h5 {
    color: #333;
    margin-bottom: 1rem;
}

.confirmation-dialog .modal-body p {
    color: #6c757d;
    margin-bottom: 0;
}

/* Success/Error Icons in Modals */
.modal-icon-success {
    color: #198754;
    font-size: 1.25rem;
}

.modal-icon-error {
    color: #dc3545;
    font-size: 1.25rem;
}

.modal-icon-warning {
    color: #ffc107;
    font-size: 1.25rem;
}

.modal-icon-info {
    color: #0dcaf0;
    font-size: 1.25rem;
}

/* Golden Select Preset Button */
.btn-select-preset {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
    border: 2px solid #DAA520;
    color: #8B4513;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 0.5rem 1rem;
}

.btn-select-preset:hover {
    background: linear-gradient(135deg, #FFED4E 0%, #FFB347 50%, #FF7F50 100%);
    border-color: #B8860B;
    color: #654321;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(255, 215, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.btn-select-preset:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-select-preset:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.5);
}

.btn-select-preset i {
    margin-right: 0.5rem;
}

/* Reusable compact action buttons for modals and lists */
.btn-action {
    --btn-bg: #f1f3f5;
    --btn-color: #212529;
    --btn-border: #e9ecef;
    background: var(--btn-bg);
    color: var(--btn-color);
    border: 1px solid var(--btn-border);
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    line-height: 1.2;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-action:hover {
    filter: brightness(0.98);
}

.btn-action i {
    font-size: 0.9em;
}

.btn-action-select {
    --btn-bg: #e8f7e9;
    --btn-color: #0f5132;
    --btn-border: #badbcc;
    border-radius: 8px;
    font-size: 18px;
}

.btn-action-edit {
    --btn-bg: #e7f1ff;
    --btn-color: #084298;
    --btn-border: #b6d4fe;
    border-radius: 8px;
    font-size: 18px;
}

.btn-action-delete {
    --btn-bg: #fbe9eb;
    --btn-color: #842029;
    --btn-border: #f5c2c7;
    border-radius: 8px;
    font-size: 18px;
}

.btn-action-toggle {
    --btn-bg: #eef1f4;
    --btn-color: #343a40;
    --btn-border: #dee2e6;
    border-radius: 8px;
    font-size: 18px;
}

/* ===== Preset Modal (Pure CSS list, no Bootstrap grid) ===== */
.preset-empty {
    text-align: center;
    color: #6c757d;
    padding: 1rem;
}

#presetModal #presetList {
    padding: 0.25rem;
}

#presetModal .preset-item {
    display: grid;
    grid-template-columns: 40px 1fr auto;
    gap: 0.75rem;
    align-items: center;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 3px 10px;
    background: #ffcf6b;
    margin-bottom: 2px;
}

/* Ensure style.css fixed height does not constrain modal list items */
#presetModal .preset-item {
    height: auto;
}

#presetModal .preset-item-left {
    display: flex;
    justify-content: center;
    align-items: center;
}

#presetModal .drag-handle {
    cursor: grab;
    color: #adb5bd;
}

#presetModal .drag-handle:active {
    cursor: grabbing;
}

#presetModal .preset-item-main {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

#presetModal .preset-title-line {
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    gap: 0.4rem 0.6rem;
}

#presetModal .preset-name {
    font-weight: 600;
    color: #212529;
}

#presetModal .preset-category {
    color: #0d6efd;
}

#presetModal .preset-desc {
    color: #6c757d;
}

#presetModal .preset-price-line {
    display: flex;
    align-items: center;
    gap: 0.6rem;
}

#presetModal .price-original {
    color: #00b6ff;
    text-decoration: line-through;
    font-size: 20px;
}

#presetModal .price-special {
    display: inline-block;
    background: #ff8d00;
    color: #ffffff;
    padding: 3px 8px;
    border-radius: 999px;
    font-weight: 600;
}

#presetModal .price-discount {
    color: #ff0000;
    font-size: 20px;
    font-weight: 600;
}

#presetModal .price-default {
    display: inline-block;
    background: #ffbc00;
    color: #ffffff;
    padding: 3px 8px;
    border-radius: 999px;
    font-weight: 600;
}

#presetModal .preset-item-actions {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
}

.btn-action .btn-text {
    display: none;
}

@media (min-width: 576px) {
    .btn-action .btn-text {
        display: inline;
        margin-left: 0.15rem;
    }
}

/* Right-side quick navigation for main sections */
.quick-nav {
    position: fixed;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 999;
    pointer-events: none;
}

.quick-nav a {
    width: 40px;
    height: 40px;
    display: grid;
    place-items: center;
    background: rgba(255, 255, 255, 0.95);
    color: #0d6efd;
    border: 1px solid #e9ecef;
    border-radius: 999px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: transform 0.15s ease, box-shadow 0.15s ease, background 0.15s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    font-size: 0.875rem;
    pointer-events: auto;
}

.quick-nav a:hover {
    transform: translateY(-2px);
    background: rgba(248, 249, 250, 0.95);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    color: #0d6efd;
    text-decoration: none;
}

.quick-nav a:active {
    transform: translateY(0);
}

/* Ensure content doesn't sit under the fixed quick nav on wider screens */
.quicknav-safe {
    padding-right: 0;
}

@media (min-width: 992px) {
    .quicknav-safe {
        padding-right: 60px;
    }
}

/* Hide quick nav on smaller screens to prevent interference */
@media (max-width: 991px) {
    .quick-nav {
        display: none;
    }
}

/* Language Dropdown - Ensure it's always on top */
.navbar .dropdown-menu {
    z-index: 9999 !important;
}

.navbar .nav-item.dropdown {
    position: relative;
    z-index: 9998;
}