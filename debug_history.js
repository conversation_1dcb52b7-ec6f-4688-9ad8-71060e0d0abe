// Debug script for History button issue
console.log('=== History Button Debug ===');

// Check if UIManager exists
if (typeof UIManager !== 'undefined') {
    console.log('✓ UIManager is available');
    
    // Check if showSection method exists
    if (typeof UIManager.showSection === 'function') {
        console.log('✓ UIManager.showSection method exists');
    } else {
        console.log('✗ UIManager.showSection method not found');
    }
    
    // Check if loadReceiptHistory method exists
    if (typeof UIManager.loadReceiptHistory === 'function') {
        console.log('✓ UIManager.loadReceiptHistory method exists');
    } else {
        console.log('✗ UIManager.loadReceiptHistory method not found');
    }
    
    // Check if displayReceiptHistoryWithBatch method exists
    if (typeof UIManager.displayReceiptHistoryWithBatch === 'function') {
        console.log('✓ UIManager.displayReceiptHistoryWithBatch method exists');
    } else {
        console.log('✗ UIManager.displayReceiptHistoryWithBatch method not found');
    }
} else {
    console.log('✗ UIManager not available');
}

// Check if global showSection function exists
if (typeof showSection === 'function') {
    console.log('✓ Global showSection function exists');
} else {
    console.log('✗ Global showSection function not found');
}

// Check if sections exist in DOM
const createSection = document.getElementById('createSection');
const historySection = document.getElementById('historySection');

if (createSection) {
    console.log('✓ createSection element found');
    console.log('  - Display:', createSection.style.display);
    console.log('  - Classes:', createSection.className);
} else {
    console.log('✗ createSection element not found');
}

if (historySection) {
    console.log('✓ historySection element found');
    console.log('  - Display:', historySection.style.display);
    console.log('  - Classes:', historySection.className);
} else {
    console.log('✗ historySection element not found');
}

// Test showSection function
console.log('=== Testing showSection function ===');
try {
    if (typeof showSection === 'function') {
        console.log('Calling showSection("history")...');
        showSection('history');
        
        setTimeout(() => {
            const historySection = document.getElementById('historySection');
            if (historySection) {
                console.log('After showSection("history"):');
                console.log('  - Display:', historySection.style.display);
                console.log('  - Classes:', historySection.className);
                console.log('  - Visible:', historySection.style.display === 'block' || historySection.classList.contains('active'));
            }
        }, 100);
    }
} catch (error) {
    console.log('✗ Error calling showSection:', error.message);
}

console.log('=== Debug Complete ===');