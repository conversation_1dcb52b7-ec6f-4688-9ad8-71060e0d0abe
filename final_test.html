<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Test - History Button</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/fontawesome.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-desktop me-2"></i>
                KMS PC Receipt Maker - Test
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link active" href="#" onclick="showSection('create')">Create Receipt</a>
                <a class="nav-link" href="#" onclick="showSection('history')">History</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Create Section -->
        <div id="createSection" class="section active">
            <div class="card">
                <div class="card-header">
                    <h5>Create Receipt Section</h5>
                </div>
                <div class="card-body">
                    <p>This is the create receipt section.</p>
                    <button class="btn btn-primary" onclick="showSection('history')">
                        Go to History
                    </button>
                </div>
            </div>
        </div>

        <!-- History Section -->
        <div id="historySection" class="section">
            <div class="card">
                <div class="card-header">
                    <h5>Receipt History</h5>
                </div>
                <div class="card-body">
                    <div id="receiptHistory">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                            <p>Loading receipt history...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>Debug Information</h5>
            </div>
            <div class="card-body">
                <div id="debugInfo">
                    <p>Debug information will appear here...</p>
                </div>
                <button class="btn btn-info btn-sm" onclick="runDebug()">
                    Run Debug
                </button>
            </div>
        </div>
    </div>

    <!-- Load all required scripts -->
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/core/app-initializer.js"></script>
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    <script src="js/main-new.js"></script>
    <script src="js/receipt-delete.js"></script>

    <script>
        function runDebug() {
            const debugInfo = document.getElementById('debugInfo');
            let output = '<h6>Debug Results:</h6>';
            
            // Check UIManager
            if (typeof UIManager !== 'undefined') {
                output += '<div class="alert alert-success">✓ UIManager is available</div>';
                
                // Check methods
                const methods = ['showSection', 'loadReceiptHistory', 'displayReceiptHistory', 'displayReceiptHistoryWithBatch'];
                methods.forEach(method => {
                    if (typeof UIManager[method] === 'function') {
                        output += `<div class="alert alert-success">✓ UIManager.${method} exists</div>`;
                    } else {
                        output += `<div class="alert alert-danger">✗ UIManager.${method} missing</div>`;
                    }
                });
            } else {
                output += '<div class="alert alert-danger">✗ UIManager not available</div>';
            }
            
            // Check global functions
            if (typeof showSection === 'function') {
                output += '<div class="alert alert-success">✓ Global showSection function exists</div>';
            } else {
                output += '<div class="alert alert-danger">✗ Global showSection function missing</div>';
            }
            
            // Check sections
            const createSection = document.getElementById('createSection');
            const historySection = document.getElementById('historySection');
            
            if (createSection) {
                output += `<div class="alert alert-info">createSection - Display: ${createSection.style.display}, Classes: ${createSection.className}</div>`;
            }
            
            if (historySection) {
                output += `<div class="alert alert-info">historySection - Display: ${historySection.style.display}, Classes: ${historySection.className}</div>`;
            }
            
            debugInfo.innerHTML = output;
        }

        // Auto-run debug on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                runDebug();
            }, 1000);
        });
    </script>
</body>
</html>