<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-lang="app_title">KMS PC Receipt Maker</title>
    
    <!-- Bootstrap CSS (local) -->
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome (local) -->
    <link href="css/fontawesome.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/receipt-items.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
    <link href="css/custom-modals.css" rel="stylesheet">
</head>
<body>
    <!-- Right-side Quick Navigation -->
    <nav class="quick-nav" aria-label="Section shortcuts">
        <a href="#anchorCreate" title="Create New Receipt" aria-label="Create New Receipt">
            <i class="fas fa-file-circle-plus"></i>
        </a>
        <a href="#anchorAddItem" title="Add Item" aria-label="Add Item">
            <i class="fas fa-plus"></i>
        </a>
        <a href="#anchorReceiptItems" title="Receipt Items" aria-label="Receipt Items">
            <i class="fas fa-list"></i>
        </a>
        <a href="#anchorReceiptPreview" title="Receipt Preview" aria-label="Receipt Preview">
            <i class="fas fa-eye"></i>
        </a>
    </nav>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-desktop me-2"></i>
                <span data-lang="app_title">KMS PC Receipt Maker</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showSection('create')" data-lang="nav_create">Create Receipt</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('history')" data-lang="nav_history">History</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-language"></i> <span data-lang="language">Language</span>
                        </a>
                        <ul class="dropdown-menu" style="z-index: 9999;">
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('zh')">繁體中文</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('en')">English</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
            
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4 quicknav-safe">
        <!-- Create Receipt Section -->
        <div id="createSection" class="section active">
            <!-- Receipt Form -->
            <div class="card mb-1" id="anchorCreate">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        <span data-lang="create_receipt">創建新收據</span>
                    </h5>
                </div>
                <div class="card-body">
                    <form id="receiptForm">
                        <div class="row">
                            <!-- Receipt Info -->
                            <div class="col-md-6 mb-1">
                                <h6 class="text-primary border-bottom pb-2 mb-3" data-lang="receipt_info">收據信息</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="receiptNumber" class="form-label" data-lang="receipt_number">收據編號</label>
                                        <input type="text" class="form-control" id="receiptNumber" placeholder="自動生成">
                                    </div>

    

                                    <div class="col-12 mb-3">
                                        <label for="logoUpload" class="form-label" data-lang="upload_logo">上傳 Logo</label>
                                        <input type="file" class="form-control" id="logoUpload" accept="image/*" onchange="handleLogoUpload(event)">
                                        <small class="text-muted" data-lang="logo_upload_hint">支持 JPG, PNG, GIF 格式，無檔案大小限制</small>

                                        <!-- Logo 預覽區域 -->
                                        <div id="logoPreview" class="logo-preview mt-3">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0" data-lang="logo_preview">Logo 預覽</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <img id="logoPreviewImage" src="" alt="Logo Preview" class="img-fluid rounded">
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h6 data-lang="logo_info">圖片資訊</h6>
                                                            <ul class="list-unstyled">
                                                                <li><strong data-lang="file_name">檔案名稱:</strong> <span id="logoFileName"></span></li>
                                                                <li><strong data-lang="file_type">檔案類型:</strong> <span id="logoFileType"></span></li>
                                                                <li><strong data-lang="file_size">檔案大小:</strong> <span id="logoFileSize"></span></li>
                                                                <li><strong data-lang="image_dimensions">圖片尺寸:</strong> <span id="logoImageDimensions"></span></li>
                                                            </ul>
                                                            <button type="button" class="btn btn-danger btn-sm" onclick="removeLogo()">
                                                                <i class="fas fa-trash me-1"></i>
                                                                <span data-lang="remove_logo">移除 Logo</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Info -->
                            <div class="col-md-6 mb-1">
                                <h6 class="text-primary border-bottom pb-2 mb-3" data-lang="customer_info">客戶信息</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="customerName" class="form-label" data-lang="customer_name">客戶姓名</label>
                                        <input type="text" class="form-control" id="customerName">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="customerPhone" class="form-label" data-lang="customer_phone">聯絡電話</label>
                                        <input type="tel" class="form-control" id="customerPhone">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="customerEmail" class="form-label" data-lang="customer_email">電子郵件</label>
                                        <input type="email" class="form-control" id="customerEmail">
                                    </div>

                                    <div class="col-12 mb-3">
                                        <label for="customerAddress" class="form-label" data-lang="customer_address">地址</label>
                                        <textarea class="form-control" id="customerAddress" rows="2"></textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label for="notes" class="form-label" data-lang="notes">備註</label>
                                        <textarea class="form-control" id="notes" rows="2"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Add Item Section -->
            <div class="card mb-1" id="anchorAddItem">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-plus me-2"></i>
                            <span data-lang="add_item">Add Item</span>
                        </h5>
                        <button type="button" class="btn btn-select-preset btn-sm" onclick="showPresetModal()">
                            <i class="fas fa-list me-1"></i>
                            <span data-lang="select_preset">Select Preset</span>
                        </button>
                    </div>
                </div>
                <div class="card-body text-center">
                    <p class="text-muted mb-3" data-lang="add_item_description">Click the button below to add items to the receipt</p>
                    <button type="button" class="btn btn-primary btn-lg" onclick="showAddItemModal()">
                        <i class="fas fa-plus-circle me-2"></i>
                        <span data-lang="add_item">Add Item</span>
                    </button>
                </div>
            </div>

            <!-- Items List Section -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="card mb-1" id="anchorReceiptItems">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    <span data-lang="receipt_items">Receipt Items</span>
                                </h5>
                                <div class="d-flex gap-2">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="UIManager.toggleCompactView()" title="Toggle Compact View">
                                            <i class="fas fa-compress-alt"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="UIManager.toggleDragAndDrop()" title="Toggle Drag & Drop">
                                            <i class="fas fa-arrows-alt"></i>
                                        </button>
                                    </div>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllItems()">
                                        <i class="fas fa-trash-alt me-1"></i>
                                        <span data-lang="clear_all">Clear All</span>
                                    </button>
                                </div>
                                <div class="d-flex gap-3">
                                    <div class="form-group mb-0">
                                        <label for="discountAmount" class="form-label me-2" data-lang="discount">Discount:</label>
                                        <input type="number" class="form-control form-control-sm d-inline-block discount-input" id="discountAmount" min="0" step="0.01" value="0">
                                    </div>
                                    <div class="form-group mb-0">
                                        <label for="taxRate" class="form-label me-2" data-lang="tax_rate">Tax Rate:</label>
                                        <input type="number" class="form-control form-control-sm d-inline-block tax-input" id="taxRate" min="0" max="100" step="0.1" value="0">
                                        <span class="text-muted">%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="receiptItemsList">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                    <p data-lang="no_items">尚未添加任何項目</p>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-primary" onclick="generateReceipt()">
                                            <i class="fas fa-file-invoice me-1"></i>
                                            <span data-lang="generate_receipt">生成收據</span>
                                        </button>
                                        <button type="button" class="btn btn-outline-success" onclick="saveReceiptConfiguration()">
                                            <i class="fas fa-save me-1"></i>
                                            <span data-lang="save_configuration">保存配置</span>
                                        </button>
                                        <button type="button" class="btn btn-outline-info" onclick="showConfigurationModal()">
                                            <i class="fas fa-cog me-1"></i>
                                            <span data-lang="manage_configurations">管理配置</span>
                                        </button>
                                    </div>
                                    <div id="totalsDisplay" class="text-end">
                                        <!-- Totals will be displayed here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Receipt Preview -->
                <div class="col-12">
                    <div class="card mt-3" id="anchorReceiptPreview">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-eye me-2"></i>
                                <span data-lang="receipt_preview">收據預覽</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="receiptPreview" class="receipt-preview">
                                <div class="text-center text-muted">
                                    <i class="fas fa-file-invoice fa-3x mb-3"></i>
                                    <p data-lang="preview_placeholder">收據預覽將在這裡顯示</p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

            </div>

            <!-- Bottom Actions: Always visible Save/Print buttons centered at the bottom -->
            <div id="bottomActions" class="text-center my-4">
                <div class="btn-group" role="group" aria-label="Receipt actions">
                    <button type="button" class="btn btn-success" onclick="saveReceipt()">
                        <i class="fas fa-save me-1"></i>
                        <span data-lang="save_receipt">保存收據</span>
                    </button>
                    <button type="button" class="btn btn-info" onclick="printReceipt()">
                        <i class="fas fa-print me-1"></i>
                        <span data-lang="print_receipt">打印收據</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- History Section -->
        <div id="historySection" class="section">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i>
                            <span data-lang="receipt_history">收據歷史</span>
                        </h5>
                        <div class="d-flex gap-2 align-items-center flex-wrap">
                            <input type="text" class="form-control form-control-sm" id="searchInput" data-lang="search_receipts" placeholder="搜索收據..." style="width: 200px;" onkeypress="handleSearchKeyPress(event)">
                            <select class="form-select form-select-sm" id="paymentFilter" style="width: 120px;" onchange="searchReceipts()">
                                <option value="" data-lang="all_payment_methods">所有付款方式</option>
                                <option value="Cash">Cash</option>
                                <option value="Venmo">Venmo</option>
                                <option value="Zelle">Zelle</option>
                                <option value="Square">Square</option>
                                <option value="Stripe">Stripe</option>
                            </select>
                            <input type="date" class="form-control form-control-sm" id="dateFromFilter" style="width: 150px;" onchange="searchReceipts()">
                            <span class="text-muted">至</span>
                            <input type="date" class="form-control form-control-sm" id="dateToFilter" style="width: 150px;" onchange="searchReceipts()">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="searchReceipts()">
                                <i class="fas fa-search"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                                <i class="fas fa-times"></i>
                            </button>
                            <div class="vr"></div>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="selectAllReceipts()" title="Select All">
                                <i class="fas fa-check-square"></i>
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="clearSelection()" title="Clear Selection">
                                <i class="fas fa-square"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="receiptHistory">
                        <!-- History will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Preset Selection Modal (Pure CSS) -->
    <div id="presetModal" class="kms-modal" aria-hidden="true" role="dialog" aria-modal="true">
        <div class="kms-modal-dialog" role="document">
            <div class="kms-modal-header">
                <h5 class="kms-modal-title" data-lang="select_preset">選擇預設項目</h5>
                <div class="toolbar-row">
                    <button type="button" class="btn-action btn-action-select" onclick="showAddPresetForm()" title="新增預設">
                        <i class="fas fa-plus me-1"></i>
                        <span data-lang="add_preset">新增預設</span>
                    </button>
                    <button type="button" class="kms-modal-close" onclick="hidePresetModal()" aria-label="Close">×</button>
                </div>
            </div>
            <div class="kms-modal-body">
                <!-- Add Preset Form -->
                <div id="addPresetForm" class="card mb-3" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0" data-lang="add_preset">新增預設項目</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-grid">
                            <div class="col-6">
                                <label class="kms-label" for="presetItemName" data-lang="item_name">項目名稱</label>
                                <input type="text" class="kms-input" id="presetItemName" required>
                            </div>
                            <div class="col-6">
                                <label class="kms-label" for="presetItemCategory" data-lang="item_category">分類</label>
                                <select class="kms-select" id="presetItemCategory">
                                    <option value="PC Case">PC Case</option>
                                    <option value="CPU">CPU</option>
                                    <option value="CPU Cooler">CPU Cooler</option>
                                    <option value="GPU">GPU</option>
                                    <option value="RAM">RAM</option>
                                    <option value="SSD">SSD</option>
                                    <option value="Motherboard">Motherboard</option>
                                    <option value="PSU">PSU</option>
                                    <option value="MB RGB">MB RGB</option>
                                    <option value="GPU RGB">GPU RGB</option>
                                    <option value="Fan RGB">Fan RGB</option>
                                    <option value="Other">Other</option>
                                    <option value="Services">Services</option>
                                </select>
                            </div>
                            <div class="col-4">
                                <label class="kms-label" for="presetItemOriginalPrice" data-lang="original_price">原價</label>
                                <input type="number" class="kms-input" id="presetItemOriginalPrice" min="0" step="0.01">
                            </div>
                            <div class="col-4">
                                <label class="kms-label" for="presetItemSpecialPrice" data-lang="special_price">特價</label>
                                <input type="number" class="kms-input" id="presetItemSpecialPrice" min="0" step="0.01">
                            </div>
                            <div class="col-4">
                                <label class="kms-label" for="presetItemDiscountPercent" data-lang="discount_percentage">折扣百分比</label>
                                <div class="input-inline">
                                    <input type="number" class="kms-input" id="presetItemDiscountPercent" readonly>
                                    <span class="input-suffix">%</span>
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="kms-label" for="presetItemDescription" data-lang="item_description">描述</label>
                                <input type="text" class="kms-input" id="presetItemDescription">
                            </div>
                            <div class="col-12">
                                <div class="toolbar-row">
                                    <button type="button" class="btn-action btn-action-select" onclick="savePresetItem()">
                                        <i class="fas fa-save me-1"></i>
                                        <span data-lang="save">保存</span>
                                    </button>
                                    <button type="button" class="btn-action btn-action-toggle" onclick="cancelAddPreset()">
                                        <i class="fas fa-times me-1"></i>
                                        <span data-lang="cancel">取消</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="toolbar-row">
                    <div class="grow">
                        <input type="text" class="kms-input" id="presetSearch" data-lang="search_items" placeholder="搜索項目..." onkeyup="filterPresets()">
                    </div>
                    <div>
                        <select class="kms-select" id="presetCategoryFilter" onchange="filterPresets()">
                            <option value="" data-lang="all_categories">所有分類</option>
                            <option value="PC Case">PC Case</option>
                            <option value="CPU">CPU</option>
                            <option value="CPU Cooler">CPU Cooler</option>
                            <option value="GPU">GPU</option>
                            <option value="RAM">RAM</option>
                            <option value="SSD">SSD</option>
                            <option value="Motherboard">Motherboard</option>
                            <option value="PSU">PSU</option>
                            <option value="MB RGB">MB RGB</option>
                            <option value="GPU RGB">GPU RGB</option>
                            <option value="Fan RGB">Fan RGB</option>
                            <option value="Other">Other</option>
                            <option value="Services">Services</option>
                        </select>
                    </div>
                </div>
                <div id="presetList">
                    <!-- Preset items will be loaded here -->
                </div>
            </div>
            <div class="kms-modal-footer">
                <button type="button" class="btn-action btn-action-toggle" onclick="hidePresetModal()" data-lang="close">關閉</button>
            </div>
        </div>
    </div>

    <!-- Receipt Modal -->
    <div class="modal fade" id="receiptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" data-lang="receipt_details">收據詳情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="receiptModalContent">
                        <!-- Receipt content will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-info" onclick="printModalReceipt()">
                        <i class="fas fa-print me-1"></i>
                        <span data-lang="print_receipt">打印收據</span>
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-lang="close">關閉</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Management Modal -->
    <div class="modal fade" id="configurationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" data-lang="manage_configurations">管理配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="configurationList" style="max-height: 400px; overflow-y: auto;">
                        <!-- Configuration items will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-lang="close">關閉</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Item Modal -->
    <div class="modal fade" id="addItemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus-circle me-2"></i>
                        <span data-lang="add_item">Add Item</span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addItemModalForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label" data-lang="item_name">Item Name *</label>
                                <input type="text" class="form-control" id="modalItemName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label" data-lang="item_category">Category</label>
                                <select class="form-select" id="modalItemCategory">
                                    <option value="PC Case">PC Case</option>
                                    <option value="CPU">CPU</option>
                                    <option value="CPU Cooler">CPU Cooler</option>
                                    <option value="GPU">GPU</option>
                                    <option value="RAM">RAM</option>
                                    <option value="SSD">SSD</option>
                                    <option value="Motherboard">Motherboard</option>
                                    <option value="PSU">PSU</option>
                                    <option value="MB RGB">MB RGB</option>
                                    <option value="GPU RGB">GPU RGB</option>
                                    <option value="Fan RGB">Fan RGB</option>
                                    <option value="Other">Other</option>
                                    <option value="Services">Services</option>
                                </select>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label" data-lang="item_description">Description</label>
                                <textarea class="form-control" id="modalItemDescription" rows="2"></textarea>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label" data-lang="quantity">Quantity *</label>
                                <input type="number" class="form-control" id="modalItemQuantity" min="1" value="1" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label" data-lang="original_price">Original Price</label>
                                <input type="number" class="form-control" id="modalItemOriginalPrice" min="0" step="0.01">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label" data-lang="special_price">Special Price *</label>
                                <input type="number" class="form-control" id="modalItemSpecialPrice" min="0" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label" data-lang="discount_percentage">Discount Percentage</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="modalItemDiscountPercent" readonly>
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label" data-lang="total_price">Total Price</label>
                                <input type="number" class="form-control" id="modalItemTotal" readonly>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="modalItemHidePrice">
                                    <label class="form-check-label" for="modalItemHidePrice" data-lang="hide_price">
                                        Hide Price (Show N/A on receipt)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-lang="cancel">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="confirmAddModalItem()">
                        <i class="fas fa-check me-1"></i>
                        <span data-lang="confirm_add">Confirm Add</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS (local) -->
    <script src="js/bootstrap.bundle.min.js"></script>
    
    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- html2canvas for converting HTML to canvas -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <!-- Custom JS - Load in order -->
    <script src="js/custom-modals.js"></script>
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>

    <!-- Core modules (load first) -->
    <script src="js/core/app-initializer.js"></script>

    <!-- Feature modules -->
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>

    <!-- Main coordinator -->
    <script src="js/main-new.js"></script>

    <!-- Receipt delete functionality -->
    <script src="js/receipt-delete.js"></script>

    <!-- Legacy modules that depend on new system -->
    <script src="js/preset-manager.js"></script>
    <script src="js/config-manager.js"></script>
    <script src="js/receipt.js"></script>
</body>
</html>
