/**
 * Bootstrap Replacement JavaScript
 * 替代 Bootstrap JavaScript 功能的簡單實現
 */

// 模態框功能
class Modal {
    constructor(element) {
        this.element = element;
        this.backdrop = null;
    }

    show() {
        // 創建背景遮罩
        this.backdrop = document.createElement('div');
        this.backdrop.className = 'modal-backdrop fade show';
        document.body.appendChild(this.backdrop);

        // 顯示模態框
        this.element.style.display = 'block';
        this.element.classList.add('show');
        document.body.classList.add('modal-open');

        // 點擊背景關閉
        this.backdrop.addEventListener('click', () => this.hide());
    }

    hide() {
        // 隱藏模態框
        this.element.style.display = 'none';
        this.element.classList.remove('show');
        document.body.classList.remove('modal-open');

        // 移除背景遮罩
        if (this.backdrop) {
            this.backdrop.remove();
            this.backdrop = null;
        }
    }

    static getInstance(element) {
        if (!element._modalInstance) {
            element._modalInstance = new Modal(element);
        }
        return element._modalInstance;
    }
}

// 折疊功能
class Collapse {
    constructor(element) {
        this.element = element;
        this.isShown = !element.classList.contains('collapse') || element.classList.contains('show');
    }

    show() {
        this.element.classList.remove('collapse');
        this.element.classList.add('show');
        this.isShown = true;
    }

    hide() {
        this.element.classList.add('collapse');
        this.element.classList.remove('show');
        this.isShown = false;
    }

    toggle() {
        if (this.isShown) {
            this.hide();
        } else {
            this.show();
        }
    }

    static getInstance(element) {
        if (!element._collapseInstance) {
            element._collapseInstance = new Collapse(element);
        }
        return element._collapseInstance;
    }
}

// 下拉選單功能
class Dropdown {
    constructor(element) {
        this.element = element;
        this.menu = element.nextElementSibling;
        this.isShown = false;
    }

    show() {
        this.menu.style.display = 'block';
        this.isShown = true;
        
        // 點擊外部關閉
        setTimeout(() => {
            document.addEventListener('click', this.outsideClickHandler.bind(this));
        }, 0);
    }

    hide() {
        this.menu.style.display = 'none';
        this.isShown = false;
        document.removeEventListener('click', this.outsideClickHandler.bind(this));
    }

    toggle() {
        if (this.isShown) {
            this.hide();
        } else {
            this.show();
        }
    }

    outsideClickHandler(event) {
        if (!this.element.contains(event.target) && !this.menu.contains(event.target)) {
            this.hide();
        }
    }

    static getInstance(element) {
        if (!element._dropdownInstance) {
            element._dropdownInstance = new Dropdown(element);
        }
        return element._dropdownInstance;
    }
}

// 初始化事件監聽器
document.addEventListener('DOMContentLoaded', function() {
    // 處理 data-bs-toggle 屬性
    document.addEventListener('click', function(event) {
        const target = event.target.closest('[data-bs-toggle]');
        if (!target) return;

        const toggle = target.getAttribute('data-bs-toggle');
        
        switch (toggle) {
            case 'modal':
                const modalTarget = target.getAttribute('data-bs-target');
                if (modalTarget) {
                    const modal = document.querySelector(modalTarget);
                    if (modal) {
                        Modal.getInstance(modal).show();
                    }
                }
                break;

            case 'collapse':
                const collapseTarget = target.getAttribute('data-bs-target');
                if (collapseTarget) {
                    const collapse = document.querySelector(collapseTarget);
                    if (collapse) {
                        Collapse.getInstance(collapse).toggle();
                    }
                }
                break;

            case 'dropdown':
                Dropdown.getInstance(target).toggle();
                break;
        }
    });

    // 處理 data-bs-dismiss 屬性
    document.addEventListener('click', function(event) {
        const target = event.target.closest('[data-bs-dismiss]');
        if (!target) return;

        const dismiss = target.getAttribute('data-bs-dismiss');
        
        switch (dismiss) {
            case 'modal':
                const modal = target.closest('.modal');
                if (modal) {
                    Modal.getInstance(modal).hide();
                }
                break;
        }
    });

    // ESC 鍵關閉模態框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                Modal.getInstance(openModal).hide();
            }
        }
    });
});

// 全局函數，用於向後兼容
window.Modal = Modal;
window.Collapse = Collapse;
window.Dropdown = Dropdown;
