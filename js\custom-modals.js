/**
 * Custom Modal System
 * Beautiful, modern modals to replace JavaScript alerts/confirms
 */

class CustomModal {
    constructor() {
        this.currentModal = null;
    }

    /**
     * Show confirmation modal
     */
    confirm(options) {
        return new Promise((resolve) => {
            const {
                title = 'Confirm Action',
                message = 'Are you sure?',
                confirmText = 'Confirm',
                cancelText = 'Cancel',
                icon = 'fas fa-question-circle',
                type = 'confirm',
                modalClass = ''
            } = options;

            this.show({
                title,
                message,
                icon,
                type,
                modalClass,
                buttons: [
                    {
                        text: cancelText,
                        class: 'custom-modal-btn custom-modal-btn-secondary',
                        action: () => {
                            this.hide();
                            resolve(false);
                        }
                    },
                    {
                        text: confirmText,
                        class: 'custom-modal-btn custom-modal-btn-danger',
                        action: () => {
                            this.hide();
                            resolve(true);
                        }
                    }
                ]
            });
        });
    }

    /**
     * Show alert modal
     */
    alert(options) {
        return new Promise((resolve) => {
            const {
                title = 'Alert',
                message = 'Alert message',
                buttonText = 'OK',
                icon = 'fas fa-info-circle',
                type = 'info'
            } = options;

            this.show({
                title,
                message,
                icon,
                type,
                buttons: [
                    {
                        text: buttonText,
                        class: 'custom-modal-btn custom-modal-btn-primary',
                        action: () => {
                            this.hide();
                            resolve(true);
                        }
                    }
                ]
            });
        });
    }

    /**
     * Show prompt modal
     */
    prompt(options) {
        return new Promise((resolve) => {
            const {
                title = 'Input Required',
                message = 'Please enter a value:',
                placeholder = '',
                defaultValue = '',
                confirmText = 'OK',
                cancelText = 'Cancel',
                icon = 'fas fa-edit',
                type = 'input'
            } = options;

            const inputId = 'customModalInput_' + Date.now();

            this.show({
                title,
                message: `
                    <p>${message}</p>
                    <input type="text" id="${inputId}" class="custom-modal-input" 
                           placeholder="${placeholder}" value="${defaultValue}">
                `,
                icon,
                type,
                buttons: [
                    {
                        text: cancelText,
                        class: 'custom-modal-btn custom-modal-btn-secondary',
                        action: () => {
                            this.hide();
                            resolve(null);
                        }
                    },
                    {
                        text: confirmText,
                        class: 'custom-modal-btn custom-modal-btn-primary',
                        action: () => {
                            const input = document.getElementById(inputId);
                            const value = input ? input.value.trim() : '';
                            this.hide();
                            resolve(value);
                        }
                    }
                ]
            });

            // Focus on input after modal is shown
            setTimeout(() => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.focus();
                    input.select();
                }
            }, 300);
        });
    }

    /**
     * Show custom modal
     */
    show(options) {
        const {
            title,
            message,
            icon,
            type = 'info',
            buttons = [],
            modalClass = ''
        } = options;

        // Remove existing modal
        this.hide();

        // Create modal HTML
        const modalHTML = `
            <div class="custom-modal-overlay ${type}-modal" id="customModalOverlay">
                <div class="custom-modal ${modalClass}">
                    <div class="custom-modal-header">
                        <button class="custom-modal-close" onclick="customModal.hide()">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="custom-modal-icon">
                            <i class="${icon}"></i>
                        </div>
                        <h3>${title}</h3>
                    </div>
                    <div class="custom-modal-body">
                        <div class="custom-modal-message">${message}</div>
                        <div class="custom-modal-buttons">
                            ${buttons.map((btn, index) =>
                                `<button class="${btn.class}" onclick="customModal.buttonAction(${index})">${btn.text}</button>`
                            ).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.currentModal = document.getElementById('customModalOverlay');
        this.currentButtons = buttons;

        // Show modal with animation
        setTimeout(() => {
            this.currentModal.classList.add('show');
        }, 10);

        // Handle ESC key
        this.handleEscKey = (e) => {
            if (e.key === 'Escape') {
                this.hide();
            }
        };
        document.addEventListener('keydown', this.handleEscKey);

        // Handle click outside
        this.currentModal.addEventListener('click', (e) => {
            if (e.target === this.currentModal) {
                this.hide();
            }
        });
    }

    /**
     * Handle button actions
     */
    buttonAction(index) {
        if (this.currentButtons && this.currentButtons[index]) {
            this.currentButtons[index].action();
        }
    }

    /**
     * Hide modal
     */
    hide() {
        if (this.currentModal) {
            this.currentModal.classList.remove('show');
            
            setTimeout(() => {
                if (this.currentModal && this.currentModal.parentNode) {
                    this.currentModal.parentNode.removeChild(this.currentModal);
                }
                this.currentModal = null;
                this.currentButtons = null;
            }, 300);

            // Remove event listeners
            if (this.handleEscKey) {
                document.removeEventListener('keydown', this.handleEscKey);
                this.handleEscKey = null;
            }
        }
    }
}

// Create global instance
window.customModal = new CustomModal();

// Helper functions for easy use
window.showConfirm = (message, title = 'Confirm') => {
    return customModal.confirm({
        title,
        message,
        icon: 'fas fa-question-circle'
    });
};

window.showClearAllConfirm = (message, title = 'Clear All Items') => {
    return customModal.confirm({
        title,
        message,
        confirmText: 'Clear All',
        cancelText: 'Cancel',
        icon: 'fas fa-trash-alt',
        modalClass: 'clear-all-modal'
    });
};

window.showAlert = (message, title = 'Alert') => {
    return customModal.alert({
        title,
        message,
        icon: 'fas fa-info-circle'
    });
};

window.showPrompt = (message, defaultValue = '', title = 'Input Required') => {
    return customModal.prompt({
        title,
        message,
        defaultValue,
        icon: 'fas fa-edit'
    });
};

// Success modal
window.showSuccess = (message, title = 'Success') => {
    return customModal.alert({
        title,
        message,
        icon: 'fas fa-check-circle',
        type: 'success'
    });
};

// Warning modal
window.showWarning = (message, title = 'Warning') => {
    return customModal.alert({
        title,
        message,
        icon: 'fas fa-exclamation-triangle',
        type: 'warning'
    });
};

// Error modal
window.showError = (message, title = 'Error') => {
    return customModal.alert({
        title,
        message,
        icon: 'fas fa-exclamation-circle',
        type: 'error'
    });
};
