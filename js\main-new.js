/**
 * Main Application Coordinator
 * KMS PC Receipt Maker - Modular Version
 * 
 * This file coordinates between different modules and provides
 * backward compatibility for existing functionality.
 */

// Global state management
window.KMSReceiptMaker = {
    version: '2.0.0',
    modules: {
        appInitializer: null,
        itemManager: null,
        receiptGenerator: null,
        uiManager: null
    },
    initialized: false
};

/**
 * Initialize the application
 */
function initializeKMSApp() {
    if (window.KMSReceiptMaker.initialized) {
        return;
    }

    // Store module references
    window.KMSReceiptMaker.modules.appInitializer = window.AppInitializer;
    window.KMSReceiptMaker.modules.itemManager = window.ItemManager;
    window.KMSReceiptMaker.modules.receiptGenerator = window.ReceiptGenerator;
    window.KMSReceiptMaker.modules.uiManager = window.UIManager;

    // Mark as initialized
    window.KMSReceiptMaker.initialized = true;

    console.log('KMS Receipt Maker v' + window.KMSReceiptMaker.version + ' initialized successfully');
}

/**
 * Backward compatibility functions
 * These functions maintain compatibility with existing HTML onclick handlers
 */

// Item management functions (delegated to ItemManager)
function addItemToReceipt(item) {
    if (window.ItemManager) {
        return window.ItemManager.addItemToReceipt(item);
    }
    console.error('ItemManager not available');
}

function removeItemFromReceipt(itemId) {
    if (window.ItemManager) {
        return window.ItemManager.removeItemFromReceipt(itemId);
    }
    console.error('ItemManager not available');
}

function updateItemQuantity(itemId, quantity) {
    if (window.ItemManager) {
        return window.ItemManager.updateItemQuantity(itemId, quantity);
    }
    console.error('ItemManager not available');
}

function updateItemPrice(itemId, price) {
    if (window.ItemManager) {
        return window.ItemManager.updateItemPrice(itemId, price);
    }
    console.error('ItemManager not available');
}

function toggleItemPriceDisplay(itemId) {
    if (window.ItemManager) {
        return window.ItemManager.toggleItemPriceDisplay(itemId);
    }
    console.error('ItemManager not available');
}

function clearAllItems() {
    if (window.ItemManager) {
        return window.ItemManager.clearAllItems();
    }
    console.error('ItemManager not available');
}

// Add Item Modal functions
function showAddItemModal() {
    if (window.ItemManager) {
        return window.ItemManager.showAddItemModal();
    }
    console.error('ItemManager not available');
}

function confirmAddModalItem() {
    if (window.ItemManager) {
        return window.ItemManager.confirmAddModalItem();
    }
    console.error('ItemManager not available');
}

// Edit item function
function editItem(itemId) {
    if (window.ItemManager && typeof window.ItemManager.editItem === 'function') {
        return window.ItemManager.editItem(itemId);
    }
    console.error('ItemManager.editItem not available');
}

// Save item edit function
function saveItemEdit(itemId) {
    if (window.ItemManager && typeof window.ItemManager.saveItemEdit === 'function') {
        return window.ItemManager.saveItemEdit(itemId);
    }
    console.error('ItemManager.saveItemEdit not available');
}

// Receipt generation functions (delegated to ReceiptGenerator)
function generateReceipt() {
    if (window.ReceiptGenerator) {
        return window.ReceiptGenerator.generateReceipt();
    }
    console.error('ReceiptGenerator not available');
}

// Override the old generateReceipt function
window.generateReceipt = generateReceipt;

function printReceipt() {
    if (window.ReceiptGenerator) {
        return window.ReceiptGenerator.printReceipt();
    }
    console.error('ReceiptGenerator not available');
}

function saveReceipt() {
    if (window.ReceiptGenerator) {
        return window.ReceiptGenerator.saveReceipt();
    }
    console.error('ReceiptGenerator not available');
}

// UI management functions (delegated to UIManager)
function showSection(sectionName) {
    if (window.UIManager) {
        return window.UIManager.showSection(sectionName);
    }
    console.error('UIManager not available');
}

function clearForm() {
    if (window.UIManager) {
        return window.UIManager.clearForm();
    }
    console.error('UIManager not available');
}

function showMessage(message, type, duration) {
    if (window.UIManager) {
        return window.UIManager.showMessage(message, type, duration);
    }
    console.error('UIManager not available');
}

function searchReceipts() {
    if (window.UIManager) {
        return window.UIManager.searchReceipts();
    }
    console.error('UIManager not available');
}

function clearFilters() {
    if (window.UIManager) {
        return window.UIManager.clearFilters();
    }
    console.error('UIManager not available');
}

function handleSearchKeyPress(event) {
    if (window.UIManager) {
        return window.UIManager.handleSearchKeyPress(event);
    }
    console.error('UIManager not available');
}

function viewReceiptDetails(receiptId) {
    if (window.UIManager) {
        return window.UIManager.viewReceiptDetails(receiptId);
    }
    console.error('UIManager not available');
}

// App initialization functions (delegated to AppInitializer)
function handleLogoUpload(event) {
    if (window.AppInitializer) {
        return window.AppInitializer.handleLogoUpload(event);
    }
    console.error('AppInitializer not available');
}

function removeLogo() {
    if (window.AppInitializer) {
        return window.AppInitializer.removeLogo();
    }
    console.error('AppInitializer not available');
}

// Modal functions
function showPresetModal() {
    // Try presetManager first (direct approach)
    if (window.presetManager && typeof window.presetManager.showPresetModal === 'function') {
        return window.presetManager.showPresetModal();
    }
    // Fallback to UIManager
    else if (window.UIManager && typeof window.UIManager.showPresetModal === 'function') {
        return window.UIManager.showPresetModal();
    }
    // Last resort - direct modal opening
    else {
        const modal = document.getElementById('presetModal');
        if (modal) {
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        } else {
            console.error('Preset modal not found and no managers available');
        }
    }
}

function showConfigurationModal() {
    if (window.UIManager) {
        return window.UIManager.showConfigurationModal();
    }
    console.error('UIManager not available');
}

function saveReceiptConfiguration() {
    if (window.UIManager) {
        return window.UIManager.saveReceiptConfiguration();
    }
    console.error('UIManager not available');
}

// Utility functions
function formatFileSize(bytes) {
    if (window.AppInitializer) {
        return window.AppInitializer.formatFileSize(bytes);
    }
    console.error('AppInitializer not available');
}

// Global properties for backward compatibility
if (!window.hasOwnProperty('receiptItems')) {
    Object.defineProperty(window, 'receiptItems', {
        get: function() {
            return window.ItemManager ? window.ItemManager.getReceiptItems() : [];
        },
        set: function(value) {
            if (window.ItemManager) {
                window.ItemManager.setReceiptItems(value);
            }
        }
    });
}

Object.defineProperty(window, 'currentReceiptNumber', {
    get: function() {
        return window.AppInitializer ? window.AppInitializer.getCurrentReceiptNumber() : '';
    }
});

// Initialize when all modules are loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for all modules to load
    setTimeout(initializeKMSApp, 100);
});

// Export the main app object
window.KMSReceiptMaker = window.KMSReceiptMaker;
