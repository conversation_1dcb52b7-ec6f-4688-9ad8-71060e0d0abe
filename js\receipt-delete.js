/**
 * Receipt Delete Functionality
 * Handles single and batch deletion of receipts
 */

/**
 * Delete a single receipt
 */
async function deleteReceipt(receiptId) {
    if (!confirm('Are you sure you want to delete this receipt? This action cannot be undone.')) {
        return;
    }

    try {
        const response = await fetch('php/delete_receipt.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: receiptId })
        });

        const data = await response.json();

        if (data.success) {
            // Remove the receipt item from DOM
            const receiptItem = document.querySelector(`[data-receipt-id="${receiptId}"]`);
            if (receiptItem) {
                receiptItem.remove();
            }

            // Show success message
            if (window.UIManager) {
                window.UIManager.showMessage('Receipt deleted successfully', 'success');
            }

            // Reload receipt history to refresh the list
            if (window.UIManager) {
                window.UIManager.loadReceiptHistory();
            }
        } else {
            if (window.UIManager) {
                window.UIManager.showMessage('Failed to delete receipt: ' + data.message, 'error');
            }
        }
    } catch (error) {
        console.error('Error deleting receipt:', error);
        if (window.UIManager) {
            window.UIManager.showMessage('Error occurred while deleting receipt', 'error');
        }
    }
}

/**
 * Delete selected receipts (batch delete)
 */
async function deleteSelectedReceipts() {
    const selectedCheckboxes = document.querySelectorAll('.receipt-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));

    if (selectedIds.length === 0) {
        if (window.UIManager) {
            window.UIManager.showMessage('No receipts selected', 'warning');
        }
        return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedIds.length} receipt(s)? This action cannot be undone.`)) {
        return;
    }

    try {
        const response = await fetch('php/delete_receipts.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ids: selectedIds })
        });

        const data = await response.json();

        if (data.success) {
            // Remove the receipt items from DOM
            selectedIds.forEach(id => {
                const receiptItem = document.querySelector(`[data-receipt-id="${id}"]`);
                if (receiptItem) {
                    receiptItem.remove();
                }
            });

            // Hide batch controls
            const batchControls = document.getElementById('batchControls');
            if (batchControls) {
                batchControls.style.display = 'none';
            }

            // Show success message
            if (window.UIManager) {
                window.UIManager.showMessage(`${selectedIds.length} receipt(s) deleted successfully`, 'success');
            }

            // Reload receipt history to refresh the list
            if (window.UIManager) {
                window.UIManager.loadReceiptHistory();
            }
        } else {
            if (window.UIManager) {
                window.UIManager.showMessage('Failed to delete receipts: ' + data.message, 'error');
            }
        }
    } catch (error) {
        console.error('Error deleting receipts:', error);
        if (window.UIManager) {
            window.UIManager.showMessage('Error occurred while deleting receipts', 'error');
        }
    }
}

/**
 * Update batch controls visibility and selected count
 */
function updateBatchControls() {
    const selectedCheckboxes = document.querySelectorAll('.receipt-checkbox:checked');
    const batchControls = document.getElementById('batchControls');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedCheckboxes.length > 0) {
        if (batchControls) {
            batchControls.style.display = 'block';
        }
        if (selectedCount) {
            selectedCount.textContent = selectedCheckboxes.length;
        }
    } else {
        if (batchControls) {
            batchControls.style.display = 'none';
        }
    }
}

/**
 * Clear all selections
 */
function clearSelection() {
    const checkboxes = document.querySelectorAll('.receipt-checkbox');
    checkboxes.forEach(cb => {
        cb.checked = false;
    });
    updateBatchControls();
}

/**
 * Select all receipts
 */
function selectAllReceipts() {
    const checkboxes = document.querySelectorAll('.receipt-checkbox');
    checkboxes.forEach(cb => {
        cb.checked = true;
    });
    updateBatchControls();
}

// Export functions to global scope
window.deleteReceipt = deleteReceipt;
window.deleteSelectedReceipts = deleteSelectedReceipts;
window.updateBatchControls = updateBatchControls;
window.clearSelection = clearSelection;
window.selectAllReceipts = selectAllReceipts;