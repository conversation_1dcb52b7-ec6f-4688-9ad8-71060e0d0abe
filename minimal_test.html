<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Test</title>
    <style>
        .section { display: none; padding: 20px; border: 1px solid #ccc; margin: 10px; }
        .section.active { display: block !important; background: #f0f8ff; }
        .debug { background: #ffffcc; padding: 10px; margin: 10px; }
    </style>
</head>
<body>
    <h1>Minimal History Test</h1>
    
    <nav>
        <button onclick="directShowSection('create')">Direct Create</button>
        <button onclick="directShowSection('history')">Direct History</button>
        <button onclick="globalShowSection('create')">Global Create</button>
        <button onclick="globalShowSection('history')">Global History</button>
    </nav>
    
    <div id="createSection" class="section active">
        <h2>Create Section</h2>
        <p>Create content here</p>
    </div>
    
    <div id="historySection" class="section">
        <h2>History Section</h2>
        <p>History content here</p>
        <div id="receiptHistory">Receipt history will load here</div>
    </div>
    
    <div class="debug">
        <h3>Debug Console</h3>
        <div id="console"></div>
        <button onclick="runDiagnostics()">Run Diagnostics</button>
    </div>

    <script>
        // Direct implementation for testing
        function directShowSection(sectionName) {
            log(`Direct showSection called with: ${sectionName}`);
            
            // Hide all sections
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.classList.remove('active');
                section.style.display = 'none';
            });
            
            // Show target section
            const targetSection = document.getElementById(sectionName + 'Section');
            if (targetSection) {
                targetSection.classList.add('active');
                targetSection.style.display = 'block';
                log(`${sectionName}Section is now visible`);
            } else {
                log(`${sectionName}Section not found!`);
            }
        }
        
        function globalShowSection(sectionName) {
            log(`Global showSection called with: ${sectionName}`);
            if (typeof showSection === 'function') {
                showSection(sectionName);
                log('Global showSection executed');
            } else {
                log('Global showSection not available');
            }
        }
        
        function log(message) {
            const console = document.getElementById('console');
            console.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
            console.scrollTop = console.scrollHeight;
        }
        
        function runDiagnostics() {
            const console = document.getElementById('console');
            console.innerHTML = '<h4>Diagnostics:</h4>';
            
            log(`typeof showSection: ${typeof showSection}`);
            log(`typeof window.showSection: ${typeof window.showSection}`);
            log(`typeof UIManager: ${typeof UIManager}`);
            
            if (typeof UIManager !== 'undefined') {
                log(`typeof UIManager.showSection: ${typeof UIManager.showSection}`);
            }
            
            // Check sections
            const createSection = document.getElementById('createSection');
            const historySection = document.getElementById('historySection');
            
            if (createSection) {
                log(`createSection exists - display: ${createSection.style.display}, class: ${createSection.className}`);
            }
            
            if (historySection) {
                log(`historySection exists - display: ${historySection.style.display}, class: ${historySection.className}`);
            }
        }
    </script>
    
    <!-- Load the actual scripts -->
    <script src="js/modules/ui-manager.js"></script>
    <script src="js/main-new.js"></script>
    
    <script>
        // Check after scripts load
        setTimeout(() => {
            log('Scripts loaded, running diagnostics...');
            runDiagnostics();
        }, 1000);
    </script>
</body>
</html>