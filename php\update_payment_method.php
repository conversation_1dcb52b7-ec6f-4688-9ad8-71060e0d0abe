<?php
/**
 * 更新收據的付款方式
 * KMS PC Receipt Maker
 */

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('只允許POST請求', 405);
}

try {
    // 獲取JSON數據
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        Response::error('無效的JSON數據');
    }
    
    // 驗證必需字段
    if (empty($data['receipt_id'])) {
        Response::error('收據ID不能為空');
    }
    
    if (empty($data['payment_method'])) {
        Response::error('付款方式不能為空');
    }
    
    // 驗證付款方式是否有效
    $validPaymentMethods = ['Cash', 'Venmo', 'Zelle', 'Square', 'Stripe'];
    if (!in_array($data['payment_method'], $validPaymentMethods)) {
        Response::error('無效的付款方式');
    }
    
    // 清理輸入數據
    $receiptId = intval($data['receipt_id']);
    $paymentMethod = $data['payment_method'];
    
    $db = new Database();
    
    // 檢查收據是否存在
    $checkSql = "SELECT id FROM receipts WHERE id = ?";
    $receipt = $db->fetch($checkSql, [$receiptId]);
    
    if (!$receipt) {
        Response::error('收據不存在');
    }
    
    // 更新付款方式
    $updateSql = "UPDATE receipts SET payment_method = ?, updated_at = NOW() WHERE id = ?";
    $result = $db->update($updateSql, [$paymentMethod, $receiptId]);
    
    if ($result) {
        Response::success(['receipt_id' => $receiptId, 'payment_method' => $paymentMethod], '付款方式更新成功');
    } else {
        Response::error('更新付款方式失敗');
    }
    
} catch (Exception $e) {
    error_log('Update payment method error: ' . $e->getMessage());
    Response::error('更新付款方式失敗: ' . $e->getMessage());
}
