<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Debug</title>
    <style>
        .section {
            display: none;
            padding: 20px;
            border: 2px solid #ccc;
            margin: 10px 0;
        }
        .section.active {
            display: block !important;
            border-color: #007bff;
        }
        .debug {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>Simple Debug Test</h1>
    
    <button onclick="testShowSection('create')">Show Create</button>
    <button onclick="testShowSection('history')">Show History</button>
    <button onclick="checkStatus()">Check Status</button>
    
    <div id="createSection" class="section active">
        <h2>Create Section</h2>
        <p>This is the create section</p>
    </div>
    
    <div id="historySection" class="section">
        <h2>History Section</h2>
        <p>This is the history section</p>
        <div id="receiptHistory">Receipt history container</div>
    </div>
    
    <div id="debug" class="debug">
        <h3>Debug Info</h3>
        <div id="debugOutput">Loading...</div>
    </div>

    <!-- Load only essential scripts -->
    <script src="js/modules/ui-manager.js"></script>
    <script src="js/main-new.js"></script>

    <script>
        function testShowSection(sectionName) {
            console.log(`Testing showSection('${sectionName}')`);
            
            if (typeof showSection === 'function') {
                console.log('showSection function exists, calling...');
                showSection(sectionName);
            } else if (typeof window.showSection === 'function') {
                console.log('window.showSection function exists, calling...');
                window.showSection(sectionName);
            } else if (typeof UIManager !== 'undefined' && typeof UIManager.showSection === 'function') {
                console.log('UIManager.showSection exists, calling...');
                UIManager.showSection(sectionName);
            } else {
                console.log('No showSection function found!');
            }
            
            setTimeout(checkStatus, 100);
        }
        
        function checkStatus() {
            const debugOutput = document.getElementById('debugOutput');
            let output = '<h4>Current Status:</h4>';
            
            // Check functions
            output += `<p>showSection function: ${typeof showSection}</p>`;
            output += `<p>window.showSection: ${typeof window.showSection}</p>`;
            output += `<p>UIManager: ${typeof UIManager}</p>`;
            if (typeof UIManager !== 'undefined') {
                output += `<p>UIManager.showSection: ${typeof UIManager.showSection}</p>`;
            }
            
            // Check sections
            const createSection = document.getElementById('createSection');
            const historySection = document.getElementById('historySection');
            
            if (createSection) {
                output += `<p>createSection display: ${createSection.style.display}</p>`;
                output += `<p>createSection classes: ${createSection.className}</p>`;
                output += `<p>createSection visible: ${getComputedStyle(createSection).display}</p>`;
            }
            
            if (historySection) {
                output += `<p>historySection display: ${historySection.style.display}</p>`;
                output += `<p>historySection classes: ${historySection.className}</p>`;
                output += `<p>historySection visible: ${getComputedStyle(historySection).display}</p>`;
            }
            
            debugOutput.innerHTML = output;
        }
        
        // Check status on load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkStatus, 1000);
        });
        
        // Also check after a delay
        setTimeout(checkStatus, 2000);
    </script>
</body>
</html>