<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Delete Functionality</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Test Receipt Delete Functionality</h2>
        
        <div class="row">
            <div class="col-md-6">
                <h4>Single Delete Test</h4>
                <button class="btn btn-danger" onclick="testSingleDelete()">
                    Test Delete Receipt ID 1
                </button>
            </div>
            
            <div class="col-md-6">
                <h4>Batch Delete Test</h4>
                <button class="btn btn-danger" onclick="testBatchDelete()">
                    Test Delete Multiple Receipts
                </button>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h4>Price Import Test</h4>
                <button class="btn btn-primary" onclick="testPriceImport()">
                    Test Edit Receipt Price Import
                </button>
            </div>
        </div>
        
        <div id="testResults" class="mt-4"></div>
    </div>

    <!-- Load all required scripts -->
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/core/app-initializer.js"></script>
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    <script src="js/main-new.js"></script>
    <script src="js/receipt-delete.js"></script>

    <script>
        function testSingleDelete() {
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = '<div class="alert alert-info">Testing single delete...</div>';
            
            // Test if delete function exists
            if (typeof deleteReceipt === 'function') {
                testResults.innerHTML += '<div class="alert alert-success">✓ deleteReceipt function exists</div>';
                
                // Note: This will show a confirmation dialog
                testResults.innerHTML += '<div class="alert alert-warning">Click OK in the confirmation dialog to test the actual delete</div>';
                deleteReceipt(1);
            } else {
                testResults.innerHTML += '<div class="alert alert-danger">✗ deleteReceipt function not found</div>';
            }
        }

        function testBatchDelete() {
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = '<div class="alert alert-info">Testing batch delete...</div>';
            
            // Test if batch delete function exists
            if (typeof deleteSelectedReceipts === 'function') {
                testResults.innerHTML += '<div class="alert alert-success">✓ deleteSelectedReceipts function exists</div>';
                
                // Create mock checkboxes for testing
                const mockCheckboxes = `
                    <div class="mt-3">
                        <h5>Mock Receipt Selection:</h5>
                        <div class="form-check">
                            <input class="form-check-input receipt-checkbox" type="checkbox" value="1" id="receipt1">
                            <label class="form-check-label" for="receipt1">Receipt KMS-UltraVIP-0000001</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input receipt-checkbox" type="checkbox" value="2" id="receipt2">
                            <label class="form-check-label" for="receipt2">Receipt KMS-UltraVIP-0000002</label>
                        </div>
                        <button class="btn btn-danger btn-sm mt-2" onclick="deleteSelectedReceipts()">
                            Delete Selected
                        </button>
                    </div>
                `;
                testResults.innerHTML += mockCheckboxes;
            } else {
                testResults.innerHTML += '<div class="alert alert-danger">✗ deleteSelectedReceipts function not found</div>';
            }
        }

        function testPriceImport() {
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = '<div class="alert alert-info">Testing price import...</div>';
            
            // Test if UIManager and edit function exist
            if (typeof UIManager !== 'undefined' && typeof UIManager.editReceiptDetails === 'function') {
                testResults.innerHTML += '<div class="alert alert-success">✓ UIManager.editReceiptDetails function exists</div>';
                
                // Test if populateFormWithReceiptData exists
                if (typeof UIManager.populateFormWithReceiptData === 'function') {
                    testResults.innerHTML += '<div class="alert alert-success">✓ populateFormWithReceiptData function exists</div>';
                    
                    // Test with mock receipt data
                    const mockReceipt = {
                        id: 1,
                        receipt_number: 'KMS-UltraVIP-0000001',
                        customer_name: 'Test Customer',
                        customer_phone: '************',
                        items: [
                            {
                                item_name: 'Test CPU',
                                item_description: 'Intel i7-12700K',
                                category: 'CPU',
                                quantity: 1,
                                unit_price: 350.00,
                                total_price: 350.00
                            }
                        ]
                    };
                    
                    testResults.innerHTML += '<div class="alert alert-info">Testing with mock receipt data...</div>';
                    UIManager.populateFormWithReceiptData(mockReceipt);
                    testResults.innerHTML += '<div class="alert alert-success">✓ Price import test completed</div>';
                } else {
                    testResults.innerHTML += '<div class="alert alert-danger">✗ populateFormWithReceiptData function not found</div>';
                }
            } else {
                testResults.innerHTML += '<div class="alert alert-danger">✗ UIManager.editReceiptDetails function not found</div>';
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = '<div class="alert alert-info">Test page loaded. Ready for testing.</div>';
        });
    </script>
</body>
</html>