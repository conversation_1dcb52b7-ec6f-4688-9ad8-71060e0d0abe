<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fixed History Button</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/fontawesome.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-desktop me-2"></i>
                KMS PC Receipt Maker - Fixed Test
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link active" href="#" onclick="showSection('create')">Create Receipt</a>
                <a class="nav-link" href="#" onclick="showSection('history')">History</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Create Section -->
        <div id="createSection" class="section active">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-plus-circle me-2"></i>Create Receipt Section</h5>
                </div>
                <div class="card-body">
                    <p>This is the create receipt section.</p>
                    <button class="btn btn-primary" onclick="showSection('history')">
                        <i class="fas fa-history me-1"></i>
                        Go to History
                    </button>
                </div>
            </div>
        </div>

        <!-- History Section -->
        <div id="historySection" class="section">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history me-2"></i>Receipt History</h5>
                </div>
                <div class="card-body">
                    <div id="receiptHistory">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                            <p>Loading receipt history...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Display -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-info-circle me-2"></i>Status & Debug</h5>
            </div>
            <div class="card-body">
                <div id="statusDisplay">
                    <p>Checking system status...</p>
                </div>
                <div class="mt-3">
                    <button class="btn btn-info btn-sm" onclick="updateStatus()">
                        <i class="fas fa-refresh me-1"></i>
                        Refresh Status
                    </button>
                    <button class="btn btn-success btn-sm" onclick="testDirectCall()">
                        <i class="fas fa-play me-1"></i>
                        Test Direct Call
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Load scripts -->
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    <script src="js/main-new.js"></script>

    <script>
        function updateStatus() {
            const statusDisplay = document.getElementById('statusDisplay');
            let status = '<h6>System Status:</h6>';
            
            // Check functions
            status += `<p><strong>showSection function:</strong> ${typeof showSection}</p>`;
            status += `<p><strong>window.showSection:</strong> ${typeof window.showSection}</p>`;
            status += `<p><strong>UIManager:</strong> ${typeof UIManager}</p>`;
            
            if (typeof UIManager !== 'undefined') {
                status += `<p><strong>UIManager.showSection:</strong> ${typeof UIManager.showSection}</p>`;
                status += `<p><strong>UIManager.loadReceiptHistory:</strong> ${typeof UIManager.loadReceiptHistory}</p>`;
                status += `<p><strong>Current section:</strong> ${UIManager.getCurrentSection()}</p>`;
            }
            
            // Check sections
            const createSection = document.getElementById('createSection');
            const historySection = document.getElementById('historySection');
            
            if (createSection) {
                const createVisible = createSection.style.display === 'block' || createSection.classList.contains('active');
                status += `<p><strong>createSection visible:</strong> ${createVisible} (display: ${createSection.style.display}, classes: ${createSection.className})</p>`;
            }
            
            if (historySection) {
                const historyVisible = historySection.style.display === 'block' || historySection.classList.contains('active');
                status += `<p><strong>historySection visible:</strong> ${historyVisible} (display: ${historySection.style.display}, classes: ${historySection.className})</p>`;
            }
            
            statusDisplay.innerHTML = status;
        }
        
        function testDirectCall() {
            console.log('Testing direct UIManager.showSection call...');
            if (typeof UIManager !== 'undefined' && typeof UIManager.showSection === 'function') {
                UIManager.showSection('history');
                setTimeout(updateStatus, 100);
            } else {
                alert('UIManager.showSection not available');
            }
        }
        
        // Auto-update status
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(updateStatus, 1000);
            
            // Set up periodic status updates
            setInterval(updateStatus, 5000);
        });
        
        // Log section changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && (mutation.attributeName === 'class' || mutation.attributeName === 'style')) {
                    console.log('Section change detected:', mutation.target.id, 'classes:', mutation.target.className, 'display:', mutation.target.style.display);
                }
            });
        });
        
        // Observe section changes
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                observer.observe(section, { attributes: true });
            });
        });
    </script>
</body>
</html>