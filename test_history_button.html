<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test History Button</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Test History Button Functionality</h2>
        
        <div class="row">
            <div class="col-12">
                <h4>Navigation Test</h4>
                <button class="btn btn-primary me-2" onclick="testShowSection('create')">
                    Show Create Section
                </button>
                <button class="btn btn-secondary me-2" onclick="testShowSection('history')">
                    Show History Section
                </button>
                <button class="btn btn-info" onclick="checkSections()">
                    Check Sections
                </button>
            </div>
        </div>
        
        <div id="testResults" class="mt-4"></div>
        
        <!-- Mock sections for testing -->
        <div id="createSection" class="section active">
            <div class="card">
                <div class="card-body">
                    <h5>Create Section</h5>
                    <p>This is the create section.</p>
                </div>
            </div>
        </div>
        
        <div id="historySection" class="section">
            <div class="card">
                <div class="card-body">
                    <h5>History Section</h5>
                    <p>This is the history section.</p>
                    <div id="receiptHistory">
                        <p>Receipt history will be loaded here...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load all required scripts -->
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/core/app-initializer.js"></script>
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    <script src="js/main-new.js"></script>

    <script>
        function testShowSection(sectionName) {
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `<div class="alert alert-info">Testing showSection('${sectionName}')...</div>`;
            
            // Check if showSection function exists
            if (typeof showSection === 'function') {
                testResults.innerHTML += '<div class="alert alert-success">✓ showSection function exists</div>';
                
                try {
                    showSection(sectionName);
                    testResults.innerHTML += `<div class="alert alert-success">✓ showSection('${sectionName}') called successfully</div>`;
                    
                    // Check if section is visible
                    setTimeout(() => {
                        const targetSection = document.getElementById(sectionName + 'Section');
                        if (targetSection) {
                            const isVisible = targetSection.style.display === 'block' || targetSection.classList.contains('active');
                            if (isVisible) {
                                testResults.innerHTML += `<div class="alert alert-success">✓ ${sectionName}Section is now visible</div>`;
                            } else {
                                testResults.innerHTML += `<div class="alert alert-danger">✗ ${sectionName}Section is not visible</div>`;
                                testResults.innerHTML += `<div class="alert alert-info">Display: ${targetSection.style.display}, Classes: ${targetSection.className}</div>`;
                            }
                        } else {
                            testResults.innerHTML += `<div class="alert alert-danger">✗ ${sectionName}Section element not found</div>`;
                        }
                    }, 100);
                    
                } catch (error) {
                    testResults.innerHTML += `<div class="alert alert-danger">✗ Error calling showSection: ${error.message}</div>`;
                }
            } else {
                testResults.innerHTML += '<div class="alert alert-danger">✗ showSection function not found</div>';
            }
        }

        function checkSections() {
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = '<div class="alert alert-info">Checking all sections...</div>';
            
            const sections = document.querySelectorAll('.section');
            testResults.innerHTML += `<div class="alert alert-info">Found ${sections.length} sections</div>`;
            
            sections.forEach((section, index) => {
                const id = section.id;
                const display = section.style.display;
                const classes = section.className;
                const isVisible = display === 'block' || classes.includes('active');
                
                testResults.innerHTML += `
                    <div class="alert alert-${isVisible ? 'success' : 'secondary'}">
                        Section ${index + 1}: ${id}<br>
                        Display: ${display || 'default'}<br>
                        Classes: ${classes}<br>
                        Visible: ${isVisible ? 'Yes' : 'No'}
                    </div>
                `;
            });
        }

        // Test when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const testResults = document.getElementById('testResults');
                testResults.innerHTML = '<div class="alert alert-info">Page loaded. Ready for testing.</div>';
                
                // Check if UIManager is available
                if (typeof UIManager !== 'undefined') {
                    testResults.innerHTML += '<div class="alert alert-success">✓ UIManager is available</div>';
                } else {
                    testResults.innerHTML += '<div class="alert alert-danger">✗ UIManager not available</div>';
                }
                
                // Check if showSection is available
                if (typeof showSection === 'function') {
                    testResults.innerHTML += '<div class="alert alert-success">✓ showSection function is available</div>';
                } else {
                    testResults.innerHTML += '<div class="alert alert-danger">✗ showSection function not available</div>';
                }
            }, 1000);
        });
    </script>
</body>
</html>