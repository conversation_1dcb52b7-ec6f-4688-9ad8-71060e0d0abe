<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal & Language Dropdown</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/fontawesome.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-desktop me-2"></i>
                Test Modal & Language
            </a>
            
            <div class="navbar-nav ms-auto">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-language"></i> <span>Language</span>
                    </a>
                    <ul class="dropdown-menu" style="z-index: 9999;">
                        <li><a class="dropdown-item" href="#">繁體中文</a></li>
                        <li><a class="dropdown-item" href="#">English</a></li>
                    </ul>
                </li>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2>Modal Header Color Test</h2>
                <p>Testing modal-header background color: <strong>#00a5ff</strong></p>
                
                <div class="d-flex gap-2 mb-4">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal1">
                        Test Modal 1
                    </button>
                    <button class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#testModal2">
                        Test Modal 2
                    </button>
                    <button class="btn btn-info" onclick="testLanguageDropdown()">
                        Test Language Dropdown
                    </button>
                </div>
                
                <div class="alert alert-info">
                    <h5>Test Results:</h5>
                    <ul>
                        <li>Language dropdown should appear above all other elements</li>
                        <li>Modal headers should have background color #00a5ff</li>
                        <li>No duplicate modal-header styles should conflict</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Modal 1 -->
    <div class="modal fade" id="testModal1" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-test me-2"></i>
                        Test Modal 1
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This modal should have a <strong>#00a5ff</strong> header background.</p>
                    <p>The header should be consistent with other modals.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Modal 2 -->
    <div class="modal fade" id="testModal2" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-cog me-2"></i>
                        Test Modal 2 (Large)
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is a larger modal to test consistency.</p>
                    <p>Header background should be the same <strong>#00a5ff</strong> color.</p>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Test Content</h6>
                            <p>Some test content here.</p>
                        </div>
                        <div class="col-md-6">
                            <h6>More Content</h6>
                            <p>Additional test content.</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary">Save</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testLanguageDropdown() {
            alert('Click on the Language dropdown in the navigation bar to test if it appears above all other elements.');
        }
        
        // Test modal header colors on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const modalHeaders = document.querySelectorAll('.modal-header');
                console.log(`Found ${modalHeaders.length} modal headers`);
                
                modalHeaders.forEach((header, index) => {
                    const computedStyle = window.getComputedStyle(header);
                    const backgroundColor = computedStyle.backgroundColor;
                    console.log(`Modal header ${index + 1} background color:`, backgroundColor);
                });
            }, 1000);
        });
    </script>
</body>
</html>