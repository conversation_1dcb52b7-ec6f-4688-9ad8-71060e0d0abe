<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Receipt Details</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Test Receipt Details Function</h2>
        <p>Click the button below to test the viewReceiptDetails function:</p>
        
        <button class="btn btn-primary" onclick="testViewReceiptDetails()">
            Test View Receipt Details
        </button>
        
        <div id="testResult" class="mt-3"></div>
    </div>

    <!-- Load all required scripts -->
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/core/app-initializer.js"></script>
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    <script src="js/main-new.js"></script>

    <script>
        function testViewReceiptDetails() {
            const testResult = document.getElementById('testResult');
            
            // Check if UIManager is available
            if (typeof UIManager !== 'undefined') {
                testResult.innerHTML = '<div class="alert alert-success">✓ UIManager is available</div>';
                
                // Check if viewReceiptDetails function exists on UIManager
                if (typeof UIManager.viewReceiptDetails === 'function') {
                    testResult.innerHTML += '<div class="alert alert-success">✓ UIManager.viewReceiptDetails function exists</div>';
                } else {
                    testResult.innerHTML += '<div class="alert alert-danger">✗ UIManager.viewReceiptDetails function not found</div>';
                }
                
                // Check if global viewReceiptDetails function exists
                if (typeof viewReceiptDetails === 'function') {
                    testResult.innerHTML += '<div class="alert alert-success">✓ Global viewReceiptDetails function exists</div>';
                    
                    // Test with a sample receipt ID (this will fail gracefully if no receipt exists)
                    testResult.innerHTML += '<div class="alert alert-info">Testing with receipt ID 1...</div>';
                    viewReceiptDetails(1);
                } else {
                    testResult.innerHTML += '<div class="alert alert-danger">✗ Global viewReceiptDetails function not found</div>';
                }
            } else {
                testResult.innerHTML = '<div class="alert alert-danger">✗ UIManager not available</div>';
            }
        }

        // Test when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const testResult = document.getElementById('testResult');
                testResult.innerHTML = '<div class="alert alert-info">Page loaded. Ready for testing.</div>';
            }, 1000);
        });
    </script>
</body>
</html>