<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Save Receipt & Print Receipt Fixes</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/receipt-items.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .test-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>測試 Save Receipt 和 Print Receipt 修復</h1>
        
        <!-- Test Section 1: Basic Receipt Generation -->
        <div class="test-section">
            <h3>測試 1: 基本收據生成</h3>
            <button class="btn btn-primary" onclick="testBasicReceipt()">生成測試收據</button>
            <div id="test1-result" class="test-result"></div>
        </div>

        <!-- Test Section 2: Save Receipt with PDF -->
        <div class="test-section">
            <h3>測試 2: 保存收據並生成PDF</h3>
            <button class="btn btn-success" onclick="testSaveReceiptWithPDF()">測試保存收據</button>
            <div id="test2-result" class="test-result"></div>
        </div>

        <!-- Test Section 3: Print Receipt with Many Items -->
        <div class="test-section">
            <h3>測試 3: 打印多項目收據（測試分頁）</h3>
            <button class="btn btn-info" onclick="testPrintManyItems()">測試多項目打印</button>
            <div id="test3-result" class="test-result"></div>
        </div>

        <!-- Test Section 4: PDF Generation with Many Items -->
        <div class="test-section">
            <h3>測試 4: PDF生成多項目（測試分頁）</h3>
            <button class="btn btn-warning" onclick="testPDFManyItems()">測試PDF多項目</button>
            <div id="test4-result" class="test-result"></div>
        </div>

        <!-- Receipt Preview Area -->
        <div class="test-section">
            <h3>收據預覽</h3>
            <div id="receiptPreview" class="receipt-preview-area">
                <div class="text-center text-muted">
                    <i class="fas fa-file-invoice fa-3x mb-3"></i>
                    <p>收據預覽將顯示在這裡</p>
                </div>
            </div>
        </div>

        <!-- Hidden form elements for testing -->
        <div style="display: none;">
            <input type="text" id="customerName" value="Test Customer">
            <input type="text" id="customerPhone" value="************">
            <input type="text" id="customerEmail" value="<EMAIL>">
            <input type="text" id="customerAddress" value="123 Test Street">
            <input type="text" id="receiptNumber" value="KMS-TEST-001">
            <textarea id="notes">This is a test receipt with multiple items to verify pagination works correctly.</textarea>
            <input type="number" id="discountAmount" value="0">
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/receipt.js"></script>

    <script>
        // Test data with discount information
        const testItems = [
            { name: 'Intel Core i9-13900K', category: 'CPU', description: 'High-performance processor', quantity: 1, unitPrice: 589.99, totalPrice: 589.99, discountPercent: 0 },
            { name: 'NVIDIA RTX 4090', category: 'GPU', description: 'Top-tier graphics card', quantity: 1, unitPrice: 1599.99, totalPrice: 1439.99, discountPercent: 10 },
            { name: 'ASUS ROG Strix Z790-E', category: 'Motherboard', description: 'Gaming motherboard', quantity: 1, unitPrice: 449.99, totalPrice: 404.99, discountPercent: 10 },
            { name: 'Corsair Vengeance DDR5-5600', category: 'RAM', description: '32GB (2x16GB) kit', quantity: 1, unitPrice: 299.99, totalPrice: 269.99, discountPercent: 10 },
            { name: 'Samsung 980 PRO 2TB', category: 'Storage', description: 'NVMe SSD', quantity: 1, unitPrice: 199.99, totalPrice: 179.99, discountPercent: 10 },
            { name: 'Corsair RM850x', category: 'PSU', description: '850W 80+ Gold PSU', quantity: 1, unitPrice: 149.99, totalPrice: 134.99, discountPercent: 10 },
            { name: 'Noctua NH-D15', category: 'Cooler', description: 'CPU air cooler', quantity: 1, unitPrice: 99.99, totalPrice: 89.99, discountPercent: 10 },
            { name: 'Fractal Design Define 7', category: 'Case', description: 'Mid-tower case', quantity: 1, unitPrice: 169.99, totalPrice: 152.99, discountPercent: 10 }
        ];

        // Add many more items for pagination testing
        const manyTestItems = [...testItems];
        for (let i = 0; i < 25; i++) {
            const unitPrice = Math.random() * 500 + 50;
            const discountPercent = Math.random() > 0.5 ? Math.floor(Math.random() * 20) : 0;
            const finalPrice = unitPrice * (1 - discountPercent / 100);
            const quantity = Math.floor(Math.random() * 5) + 1;

            manyTestItems.push({
                name: `Test Component ${i + 1}`,
                category: 'Misc',
                description: `This is test component number ${i + 1} with a longer description to test text wrapping and pagination`,
                quantity: quantity,
                unitPrice: unitPrice,
                totalPrice: finalPrice * quantity,
                discountPercent: discountPercent
            });
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `test-result test-${type}`;
        }

        function testBasicReceipt() {
            try {
                // Mock ItemManager for testing
                window.ItemManager = {
                    getReceiptItems: () => testItems,
                    calculateTotals: () => ({
                        subtotal: testItems.reduce((sum, item) => sum + item.totalPrice, 0),
                        discount: 0,
                        tax: 0,
                        total: testItems.reduce((sum, item) => sum + item.totalPrice, 0)
                    })
                };

                window.ReceiptGenerator.generateReceipt();
                showResult('test1-result', '✓ 基本收據生成成功', 'success');
            } catch (error) {
                showResult('test1-result', `✗ 基本收據生成失敗: ${error.message}`, 'error');
            }
        }

        function testSaveReceiptWithPDF() {
            try {
                // Mock the save receipt functionality
                const originalFetch = window.fetch;
                window.fetch = async (url, options) => {
                    if (url.includes('save_receipt.php')) {
                        return {
                            json: async () => ({
                                success: true,
                                data: { receipt_number: 'KMS-TEST-PDF-001' }
                            })
                        };
                    }
                    return originalFetch(url, options);
                };

                // Test save receipt
                window.currentReceiptData = {
                    customer: {
                        name: 'Test Customer',
                        phone: '************',
                        email: '<EMAIL>',
                        address: '123 Test Street'
                    },
                    items: testItems,
                    totals: {
                        subtotal: testItems.reduce((sum, item) => sum + item.totalPrice, 0),
                        discount: 0,
                        tax: 0,
                        total: testItems.reduce((sum, item) => sum + item.totalPrice, 0)
                    },
                    notes: 'Test receipt for PDF generation'
                };

                saveReceipt();
                showResult('test2-result', '✓ 保存收據並生成PDF測試啟動（檢查下載文件夾）', 'success');
            } catch (error) {
                showResult('test2-result', `✗ 保存收據測試失敗: ${error.message}`, 'error');
            }
        }

        function testPrintManyItems() {
            try {
                // Mock ItemManager with many items
                window.ItemManager = {
                    getReceiptItems: () => manyTestItems,
                    calculateTotals: () => ({
                        subtotal: manyTestItems.reduce((sum, item) => sum + item.totalPrice, 0),
                        discount: 50,
                        tax: manyTestItems.reduce((sum, item) => sum + item.totalPrice, 0) * 0.08,
                        total: manyTestItems.reduce((sum, item) => sum + item.totalPrice, 0) * 1.08 - 50
                    })
                };

                window.ReceiptGenerator.printReceipt();
                showResult('test3-result', '✓ 多項目打印測試啟動（檢查打印預覽的分頁）', 'success');
            } catch (error) {
                showResult('test3-result', `✗ 多項目打印測試失敗: ${error.message}`, 'error');
            }
        }

        function testPDFManyItems() {
            try {
                const receiptData = {
                    customer: {
                        name: 'Test Customer - Many Items',
                        phone: '************',
                        email: '<EMAIL>',
                        address: '123 Test Street'
                    },
                    items: manyTestItems,
                    totals: {
                        subtotal: manyTestItems.reduce((sum, item) => sum + item.totalPrice, 0),
                        discount: 100,
                        tax: manyTestItems.reduce((sum, item) => sum + item.totalPrice, 0) * 0.08,
                        total: manyTestItems.reduce((sum, item) => sum + item.totalPrice, 0) * 1.08 - 100
                    },
                    notes: 'This is a test receipt with many items to verify that PDF pagination works correctly. The PDF should automatically create new pages when needed and maintain proper formatting throughout.'
                };

                window.ReceiptGenerator.generatePDF(receiptData, 'KMS-MULTIPAGE-TEST-001');
                showResult('test4-result', '✓ 多項目PDF生成測試啟動（檢查下載的PDF文件分頁）', 'success');
            } catch (error) {
                showResult('test4-result', `✗ 多項目PDF生成測試失敗: ${error.message}`, 'error');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded. Ready for testing.');
        });
    </script>
</body>
</html>
