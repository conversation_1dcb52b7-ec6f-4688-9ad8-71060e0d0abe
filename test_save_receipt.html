<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Save Receipt PDF</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/fontawesome.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Test Save Receipt PDF Generation</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Customer Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="customerName" class="form-label">Customer Name</label>
                            <input type="text" class="form-control" id="customerName" value="Test Customer">
                        </div>
                        <div class="mb-3">
                            <label for="customerPhone" class="form-label">Phone</label>
                            <input type="text" class="form-control" id="customerPhone" value="************">
                        </div>
                        <div class="mb-3">
                            <label for="customerEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="customerEmail" value="<EMAIL>">
                        </div>
                        <div class="mb-3">
                            <label for="customerAddress" class="form-label">Address</label>
                            <textarea class="form-control" id="customerAddress">123 Test Street, Test City</textarea>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes">Test receipt for PDF generation</textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="addTestItems()">
                                <i class="fas fa-plus me-1"></i>
                                Add Test Items
                            </button>
                            <button class="btn btn-success" onclick="testSaveReceipt()">
                                <i class="fas fa-save me-1"></i>
                                Test Save Receipt (Generate PDF)
                            </button>
                            <button class="btn btn-info" onclick="checkFunctions()">
                                <i class="fas fa-check me-1"></i>
                                Check Functions
                            </button>
                        </div>
                        
                        <div class="mt-3">
                            <h6>Test Results:</h6>
                            <div id="testResults">
                                <p>Click "Check Functions" to verify setup.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load scripts -->
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/core/app-initializer.js"></script>
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    <script src="js/main-new.js"></script>

    <script>
        function addTestItems() {
            if (typeof ItemManager !== 'undefined' && ItemManager.addItemToReceipt) {
                // Add test items
                const testItems = [
                    {
                        name: 'Intel i7-12700K CPU',
                        category: 'CPU',
                        description: 'High-performance processor',
                        quantity: 1,
                        unitPrice: 350.00,
                        totalPrice: 350.00
                    },
                    {
                        name: 'NVIDIA RTX 4070 GPU',
                        category: 'GPU',
                        description: 'Gaming graphics card',
                        quantity: 1,
                        unitPrice: 599.99,
                        totalPrice: 599.99
                    },
                    {
                        name: 'Corsair 32GB DDR4 RAM',
                        category: 'RAM',
                        description: '32GB DDR4-3200 Memory Kit',
                        quantity: 1,
                        unitPrice: 149.99,
                        totalPrice: 149.99
                    }
                ];

                testItems.forEach(item => {
                    ItemManager.addItemToReceipt(item);
                });

                document.getElementById('testResults').innerHTML += '<div class="alert alert-success">✓ Test items added successfully</div>';
            } else {
                document.getElementById('testResults').innerHTML += '<div class="alert alert-danger">✗ ItemManager not available</div>';
            }
        }

        function testSaveReceipt() {
            if (typeof saveReceipt === 'function') {
                document.getElementById('testResults').innerHTML += '<div class="alert alert-info">Calling saveReceipt()...</div>';
                saveReceipt();
            } else {
                document.getElementById('testResults').innerHTML += '<div class="alert alert-danger">✗ saveReceipt function not found</div>';
            }
        }

        function checkFunctions() {
            const testResults = document.getElementById('testResults');
            let results = '<h6>Function Check Results:</h6>';

            // Check jsPDF
            if (typeof window.jspdf !== 'undefined') {
                results += '<div class="alert alert-success">✓ jsPDF library loaded</div>';
            } else {
                results += '<div class="alert alert-danger">✗ jsPDF library not loaded</div>';
            }

            // Check ItemManager
            if (typeof ItemManager !== 'undefined') {
                results += '<div class="alert alert-success">✓ ItemManager available</div>';
            } else {
                results += '<div class="alert alert-danger">✗ ItemManager not available</div>';
            }

            // Check ReceiptGenerator
            if (typeof ReceiptGenerator !== 'undefined') {
                results += '<div class="alert alert-success">✓ ReceiptGenerator available</div>';
                
                if (typeof ReceiptGenerator.saveReceipt === 'function') {
                    results += '<div class="alert alert-success">✓ ReceiptGenerator.saveReceipt method exists</div>';
                } else {
                    results += '<div class="alert alert-danger">✗ ReceiptGenerator.saveReceipt method missing</div>';
                }

                if (typeof ReceiptGenerator.generatePDF === 'function') {
                    results += '<div class="alert alert-success">✓ ReceiptGenerator.generatePDF method exists</div>';
                } else {
                    results += '<div class="alert alert-danger">✗ ReceiptGenerator.generatePDF method missing</div>';
                }
            } else {
                results += '<div class="alert alert-danger">✗ ReceiptGenerator not available</div>';
            }

            // Check global saveReceipt
            if (typeof saveReceipt === 'function') {
                results += '<div class="alert alert-success">✓ Global saveReceipt function available</div>';
            } else {
                results += '<div class="alert alert-danger">✗ Global saveReceipt function not available</div>';
            }

            testResults.innerHTML = results;
        }

        // Auto-check on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkFunctions, 1000);
        });
    </script>
</body>
</html>